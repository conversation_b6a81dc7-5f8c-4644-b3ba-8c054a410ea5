import { useEffect, useState } from "react";
import { models } from "powerbi-client";
import { PowerBIEmbed } from "powerbi-client-react";
import api from "../services/apiService";

interface PowerBITokenResponse {
  embedToken: string;
  expiresAt: number;
  reportId: string;
  datasetId: string;
}

interface ArtifactInfoResponse {
  reportId: string;
  datasetId: string;
}

const PowerBIReport = () => {
  const [accessToken, setAccessToken] = useState<string>("");
  const [reportId, setReportId] = useState<string>("");
  const [embedUrl, setEmbedUrl] = useState<string>("");
  const [isMobile, setIsMobile] = useState<boolean>(false);
  const [hasToken, setHasToken] = useState<boolean | null>(null);
  const [isTwitterDisabled, setIsTwitterDisabled] = useState<boolean>(true);
  const [freeUserReportId, setFreeUserReportId] = useState<string>("");
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // Check user package from localStorage
  useEffect(() => {
    const userData = JSON.parse(localStorage.getItem("UserData") || "{}");
    const pkg = userData.pkg?.toLowerCase() || "unknown";
    setIsTwitterDisabled(pkg === "unknown" || pkg === "free");
  }, []);

  const fetchFreeUserArtifactInfo = async () => {
    try {
      setIsLoading(true);
      const data = await api.get<ArtifactInfoResponse>("power-bi/artifact-info");
      setFreeUserReportId(data.reportId);
      setIsLoading(false);
      return data.reportId;
    } catch (error) {
      console.error("Failed to fetch artifact info:", error);
      // Fallback to default report ID
      const fallbackReportId = "67b94680-864d-42b6-ac51-c55081b8c1fd";
      setFreeUserReportId(fallbackReportId);
      setIsLoading(false);
      return fallbackReportId;
    }
  };

  const fetchEmbedToken = async () => {
    try {
      setIsLoading(true);
      const cached = localStorage.getItem("powerbi_token");
      if (cached) {
        const parsed = JSON.parse(cached) as PowerBITokenResponse;
        const now = Date.now();

        if (parsed.expiresAt > now + 60000 * 5) {
          console.log("Using cached token");
          setAccessToken(parsed.embedToken);
          setReportId(parsed.reportId);
          setEmbedUrl(
            `https://app.powerbi.com/reportEmbed?reportId=${parsed.reportId}&ctid=ccdd6c63-ac71-4ab9-ae96-54b0443f664e&navContentPaneEnabled=false&chromeless=1`
          );
          setHasToken(true);
          setIsLoading(false);
          return;
        }
      }

      console.log("Fetching new token");
      const data = await api.get<PowerBITokenResponse>("power-bi/token");

      setAccessToken(data.embedToken);
      setReportId(data.reportId);
      setEmbedUrl(
        `https://app.powerbi.com/reportEmbed?reportId=${data.reportId}&ctid=ccdd6c63-ac71-4ab9-ae96-54b0443f664e&navContentPaneEnabled=false&chromeless=1`
      );
      localStorage.setItem("powerbi_token", JSON.stringify(data));
      setHasToken(true);
      setIsLoading(false);
    } catch (error) {
      console.error("Failed to fetch token:", error);
      setHasToken(false);
      setIsLoading(false);
      localStorage.removeItem("powerbi_token");
    }
  };

  useEffect(() => {
    // For free users, fetch artifact info
    if (isTwitterDisabled) {
      fetchFreeUserArtifactInfo();
      setHasToken(false);
    } else {
      // Only fetch token if package is not free or unknown
      fetchEmbedToken();
    }

    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    handleResize();
    window.addEventListener("resize", handleResize);

    return () => window.removeEventListener("resize", handleResize);
  }, [isTwitterDisabled]);

  if (isLoading || hasToken === null) {
    return (
      <div className="flex justify-center items-center min-h-[96vh] shadow bg-white rounded-3xl">
        <p className="text-lg text-gray-600">Loading report...</p>
      </div>
    );
  }

  // Render iframe for free/unknown packages or if token fetch fails
  if (isTwitterDisabled || hasToken === false || !accessToken || !reportId || !embedUrl) {
    return (
      <div className="flex justify-center items-center min-h-[96vh] shadow bg-white rounded-3xl w-full">
        <div className="w-full h-[90vh] rounded-3xl overflow-hidden">
          <iframe
            title="Power BI Report"
            src={`https://app.powerbi.com/reportEmbed?reportId=${freeUserReportId || "67b94680-864d-42b6-ac51-c55081b8c1fd"}&autoAuth=true&ctid=ccdd6c63-ac71-4ab9-ae96-54b0443f664e&navContentPaneEnabled=false&chromeless=1`}
            frameBorder="0"
            allowFullScreen
            className="w-full h-full min-h-[500px] xs:max-md:min-h-[400px]"
            style={{
              border: 'none',
              borderRadius: '1.5rem'
            }}
          />
        </div>
      </div>
    );
  }

  // Render PowerBIEmbed for non-free packages with valid token
  return (
    <div className="flex justify-center items-center min-h-[96vh] shadow bg-white rounded-3xl w-full">
      <div className="w-full h-[90vh] rounded-3xl overflow-hidden">
        <PowerBIEmbed
          embedConfig={{
            type: "report",
            id: reportId,
            embedUrl: embedUrl,
            accessToken: accessToken,
            tokenType: models.TokenType.Embed,
            permissions: models.Permissions.All,
            settings: {
              filterPaneEnabled: false,
              bars: {
                actionBar: {
                  visible: false,
                },
              },
              panes: {
                filters: {
                  expanded: false,
                  visible: false,
                },
                pageNavigation: {
                  visible: true,
                  position: models.PageNavigationPosition.Left,
                },
                bookmarks: {
                  visible: false,
                },
                fields: {
                  expanded: false,
                  visible: false,
                },
                selection: {
                  visible: false,
                },
                syncSlicers: {
                  visible: false,
                },
                visualizations: {
                  expanded: false,
                  visible: false,
                },
              },
              ...(isMobile && {
                layoutType: models.LayoutType.MobilePortrait,
              }),
            },
          }}
          eventHandlers={new Map([
            ["loaded", () => console.log("Report loaded")],
            ["rendered", () => console.log("Report rendered")],
            ["error", (event: any) => console.error("Power BI Error", event.detail)],
          ])}
          cssClassName="w-full h-full report-style-class"
          getEmbeddedComponent={(embeddedReport) => {
            console.log("Embedded Report instance:", embeddedReport);
          }}
        />
      </div>
    </div>
  );
};

export default PowerBIReport;
