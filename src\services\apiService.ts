import axios, { AxiosError, AxiosResponse, InternalAxiosRequestConfig } from 'axios';
import { showSuccessToast, showErrorToast, showWarnToast } from '../components/Toast';

const BASE_URL = import.meta.env.VITE_API_BASE_URL;

const apiInstance = axios.create({
  baseURL: BASE_URL,
  timeout: 20000,
  headers: {
    "Content-Type": "application/json",
  },
});

// Power BI API instance
const powerBiApiInstance = axios.create({
  baseURL: BASE_URL,
  timeout: 20000,
  headers: {
    "Content-Type": "application/json",
  },
});

// Function to get JWT token from localStorage
const getToken = (): string | null => {
  return localStorage.getItem('authToken');
};

// Function to get Microsoft access token from localStorage
const getMsAccessToken = (): string | null => {
  return JSON.parse(localStorage.getItem("UserData") || "{}").ms_access_token || null;
};

// Function to clear token and redirect user to login
const handleTokenExpiry = () => {
  localStorage.removeItem("authToken");
  localStorage.removeItem("UserData");
  localStorage.removeItem("powerbi_token");
  localStorage.removeItem("powerbi_token_free");
  showWarnToast('Session expired. Please log in again.');
  window.location.href = `/login`;
};

// Login function
const login = async (): Promise<void> => {
  try {
    const response = await apiInstance.get(`${BASE_URL}auth/login`);
    console.log(response);

    const loginUrl = response.data.loginUrl;

    if (loginUrl) {
      console.log("Redirecting to login page:", loginUrl);
      window.location.href = loginUrl;
    } else {
      showWarnToast("Login URL not found in response");
    }
  } catch (error) {
    showErrorToast("Login request failed");
    console.error("Login request failed:", error);
  }
};

// Request interceptor for general API
apiInstance.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const token = getToken();
    if (token) {
      config.headers.set('Authorization', `Bearer ${token}`);
    }
    return config;
  },
  (error) => {
    showErrorToast("Request failed. Please try again.");
    return Promise.reject(error);
  }
);

// Request interceptor for Power BI API
powerBiApiInstance.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const token = getToken();
    const msAccessToken = getMsAccessToken();
    if (token) {
      config.headers.set('Authorization', `Bearer ${token}`);
    }
    if (msAccessToken && config.url?.includes("token-free-user")) {
      config.headers.set('x-ms-access-token', msAccessToken);
    }
    return config;
  },
  (error) => {
    showErrorToast("Power BI request failed. Please try again.");
    return Promise.reject(error);
  }
);

// Response interceptor for both instances
const responseInterceptor = (response: AxiosResponse) => {
  if (response.status >= 200 && response.status < 300) {
    showSuccessToast("Request completed successfully");
  }
  return response;
};

const errorInterceptor = (error: AxiosError) => {
  if (error.response?.status === 401) {
    handleTokenExpiry();
  } else if (error.response?.status === 500) {
    showErrorToast("Server error occurred. Please try again later.");
  }
  return Promise.reject(error);
};

apiInstance.interceptors.response.use(responseInterceptor, errorInterceptor);
powerBiApiInstance.interceptors.response.use(responseInterceptor, errorInterceptor);

// General API Methods
const get = async <T>(endpoint: string): Promise<T> => {
  try {
    const response: AxiosResponse<T> = await apiInstance.get(`${BASE_URL}${endpoint}`);
    return response.data;
  } catch (error) {
    console.error("Error in GET request:", error);
    throw error;
  }
};

const getfilter = async <T>(endpoint: string, params?: Record<string, any>): Promise<T> => {
  try {
    console.log("API params:", params);
    const response: AxiosResponse<T> = await apiInstance.get(`${BASE_URL}${endpoint}`, {
      params,
      paramsSerializer: (params) => {
        return Object.entries(params)
          .map(([key, value]) => {
            if (typeof value === "string" && value.includes(",")) {
              return `${key}=${value}`;
            }
            return `${key}=${encodeURIComponent(value)}`;
          })
          .join("&");
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error in GET request:", error);
    throw error;
  }
};

const post = async <T, U>(endpoint: string, data: T): Promise<U> => {
  try {
    const response: AxiosResponse<U> = await apiInstance.post(`${BASE_URL}${endpoint}`, data);
    return response.data;
  } catch (error) {
    console.error("Error in POST request:", error);
    throw error;
  }
};

const put = async <T, U>(endpoint: string, data: T): Promise<U> => {
  try {
    const response: AxiosResponse<U> = await apiInstance.put(endpoint, data);
    return response.data;
  } catch (error) {
    console.error("Error in PUT request:", error);
    throw error;
  }
};

const putimage = async <T, U>(
  endpoint: string,
  data: T,
  customHeaders?: Record<string, string>
): Promise<U> => {
  try {
    let contentType = 'application/json';
    if (data instanceof FormData) {
      contentType = 'multipart/form-data';
    } else if (data instanceof Blob || data instanceof File) {
      contentType = ((data as unknown) as File).type || 'application/octet-stream';
    }

    const config = {
      headers: {
        'Content-Type': contentType,
        ...customHeaders
      },
      transformRequest: (data instanceof FormData || data instanceof Blob || data instanceof File)
        ? [(data: any) => data]
        : undefined
    };

    const response: AxiosResponse<U> = await apiInstance.put(endpoint, data, config);
    return response.data;
  } catch (error) {
    console.error("Error in PUT request:", error);
    throw error;
  }
};

const deleteRequest = async <T>(endpoint: string): Promise<T> => {
  try {
    const response: AxiosResponse<T> = await apiInstance.delete(endpoint);
    return response.data;
  } catch (error) {
    console.error("Error in DELETE request:", error);
    throw error;
  }
};

const deleteattribute = async <T, U>(endpoint: string, data: T): Promise<U> => {
  try {
    const response: AxiosResponse<U> = await apiInstance.delete(`${BASE_URL}${endpoint}`, {
      data,
    });
    return response.data;
  } catch (error) {
    console.error("Error in DELETE request:", error);
    throw error;
  }
};

const sociallogin = async (provider: "facebook" | "youtube" | "twitter"): Promise<void> => {
  try {
    const response = await apiInstance.get(`/linking/${provider}`);
    const loginUrl = response.data.loginUrl;

    if (loginUrl) {
      console.log(`Redirecting to ${provider} login:`, loginUrl);
      window.location.href = loginUrl;
    } else {
      showWarnToast("Login URL not found in response");
    }
  } catch (error) {
    console.error("Login request failed:", error);
  }
};

// Power BI API Methods
const getPowerBIToken = async <T>(endpoint: string): Promise<T> => {
  try {
    const response: AxiosResponse<T> = await powerBiApiInstance.get(`${BASE_URL}${endpoint}`);
    return response.data;
  } catch (error) {
    console.error("Error in Power BI GET request:", error);
    throw error;
  }
};

export default {
  get,
  post,
  put,
  putimage,
  getfilter,
  delete: deleteRequest,
  deleteattribute,
  login,
  sociallogin,
  getPowerBIToken,
};